# FastAPI Libraries
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse

# Third-Party Libraries
import uvicorn

# Standard Library Imports
import os
from dotenv import load_dotenv

app = FastAPI()


@app.post("/api/gpus/webhook/deallocation")
async def webhook_deallocation(request: Request):
    payload = await request.json()
    print("Received payload:", payload)
    # Do something with the payload if needed
    return JSONResponse(
        content={"status": "success", "message": "Webhook received"}, status_code=200
    )


@app.post("/api/gpus/webhook/status-change-warning")
async def webhook_status_change_warning(request: Request):
    payload = await request.json()
    print("Received payload:", payload)
    # Do something with the payload if needed
    return JSONResponse(
        content={"status": "success", "message": "Webhook received"}, status_code=200
    )


if __name__ == "__main__":
    load_dotenv()
    cert_path = os.getenv("CERT_LOCATION")
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=3000,
        log_level="info",
        ssl_certfile=cert_path + "/client.crt",
        ssl_keyfile=cert_path + "/client.key",
        ssl_ca_certs=cert_path + "/ca.cer",
    )
