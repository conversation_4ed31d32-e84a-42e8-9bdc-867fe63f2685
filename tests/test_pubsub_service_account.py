"""
Test script for the new service account-based PubSub implementation.

This script tests the basic functionality of the register-api pubsub module
with service account authentication.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime, timezone

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from api.pubsub import PubSubClient, MessageFactory
from api.pubsub.exceptions import ConfigurationError
from compute.pubsub.exceptions import AuthenticationError


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_pubsub_client():
    """Test the PubSub client with service account authentication."""
    
    print("=" * 60)
    print("Testing Register API PubSub with Service Account Auth")
    print("=" * 60)
    
    # Check environment variables
    credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    project_id = os.getenv('PUBSUB_PROJECT_ID')
    
    print(f"Credentials path: {credentials_path}")
    print(f"Project ID: {project_id}")
    
    if not credentials_path and not project_id:
        print("⚠️  No credentials configured. Set GOOGLE_APPLICATION_CREDENTIALS or PUBSUB_PROJECT_ID")
        print("   This test will attempt to use default application credentials.")
    
    try:
        # Initialize PubSub client
        print("\n1. Initializing PubSub client...")
        client = PubSubClient(
            credentials_path=credentials_path,
            project_id=project_id,
            timeout=10.0,
            validator_hotkey="test-validator-hotkey"
        )
        
        if client.disabled:
            print("❌ PubSub is disabled via PUBSUB_DISABLE environment variable")
            return
            
        print(f"✅ PubSub client initialized successfully")
        print(f"   Project ID: {client.project_id}")
        
        # Test message factory
        print("\n2. Testing message factory...")
        factory = MessageFactory(source="test-register-api", validator_hotkey="test-validator")
        
        # Create a test message
        message = factory.create_gpu_status_change(
            miner_hotkey="test-miner-hotkey",
            previous_status="offline",
            current_status="online",
            reason="test_connection",
            correlation_id="test-123"
        )
        
        print(f"✅ Created test message: {message.message_type}")
        print(f"   Message data: {message.data}")
        
        # Test publishing (this will likely fail without proper setup, but tests the code path)
        print("\n3. Testing message publishing...")
        
        try:
            # Test direct publish
            result = await client.publish_to_miner_events(
                message,
                async_result=False  # Use direct publish for testing
            )
            print(f"✅ Message published successfully: {result}")
            
        except Exception as e:
            print(f"⚠️  Publishing failed (expected if topics don't exist): {e}")
            print("   This is normal if you haven't set up the GCP topics yet.")
        
        # Test queue status
        print("\n4. Testing queue status...")
        queue_status = client.get_queue_status()
        print(f"✅ Queue status: {queue_status}")
        
        # Test credential refresh
        print("\n5. Testing credential refresh...")
        refresh_result = client.refresh_credentials()
        print(f"✅ Credential refresh: {refresh_result}")
        
        # Clean up
        print("\n6. Cleaning up...")
        await client.stop_queue_workers()
        client.close()
        print("✅ Client closed successfully")
        
        print("\n" + "=" * 60)
        print("✅ All tests completed successfully!")
        print("=" * 60)
        
    except AuthenticationError as e:
        print(f"❌ Authentication error: {e}")
        print("\nTroubleshooting:")
        print("1. Check that GOOGLE_APPLICATION_CREDENTIALS points to a valid service account key")
        print("2. Verify the service account has Pub/Sub permissions")
        print("3. Ensure the project ID is correct")
        
    except ConfigurationError as e:
        print(f"❌ Configuration error: {e}")
        print("\nTroubleshooting:")
        print("1. Set PUBSUB_PROJECT_ID environment variable")
        print("2. Ensure service account key includes project_id")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()


def test_message_types():
    """Test message type creation and serialization."""
    
    print("\n" + "=" * 60)
    print("Testing Message Types")
    print("=" * 60)
    
    factory = MessageFactory(source="test", validator_hotkey="test-validator")
    
    # Test GPU status change message
    print("\n1. Testing GPU status change message...")
    status_msg = factory.create_gpu_status_change(
        miner_hotkey="test-miner",
        previous_status="offline",
        current_status="online",
        reason="test"
    )
    print(f"✅ Status message: {status_msg.to_dict()}")
    
    # Test GPU deallocation message
    print("\n2. Testing GPU deallocation message...")
    dealloc_msg = factory.create_gpu_deallocation(
        miner_hotkey="test-miner",
        allocation_uuid="test-uuid",
        deallocation_reason="test_completed",
        gpu_model="RTX 4090"
    )
    print(f"✅ Deallocation message: {dealloc_msg.to_dict()}")
    
    # Test miner allocation message
    print("\n3. Testing miner allocation message...")
    alloc_msg = factory.create_miner_allocation(
        miner_hotkey="test-miner",
        allocation_result=True
    )
    print(f"✅ Allocation message: {alloc_msg.to_dict()}")
    
    print("\n✅ All message types tested successfully!")


async def main():
    """Main test function."""
    
    print("Register API PubSub Service Account Test")
    print("This script tests the new service account-based PubSub implementation.")
    print()
    
    # Test message types first (no credentials needed)
    test_message_types()
    
    # Test PubSub client (requires credentials)
    await test_pubsub_client()
    
    print("\n🎉 Test completed!")
    print("\nNext steps:")
    print("1. Set up service account credentials if not already done")
    print("2. Create the required Pub/Sub topics in your GCP project")
    print("3. Test with real message publishing")


if __name__ == "__main__":
    asyncio.run(main())
