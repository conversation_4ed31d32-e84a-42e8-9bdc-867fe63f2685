# Standard Library Imports
import asyncio

from typing import Dict, Any

# Custom/Project-Specific Libraries
from compute.protocol import Allocate
from bittensor import Dendrite, Metagraph


async def get_deregister_response_status(
    metagraph: Metagraph, hotkey: str, regkey: str, dendrite: Dendrite
) -> Dict[str, Any]:
    index = metagraph.hotkeys.index(hotkey)
    axon = metagraph.axons[index]
    allocate_class = Allocate(
        timeline=0, device_requirement={}, checking=False, public_key=regkey
    )
    deregister_response = await dendrite(axon, allocate_class, timeout=60)

    return deregister_response["status"]


def get_deregister_response_status_sync(
    metagraph: Metagraph, hotkey: str, regkey: str, dendrite: Dendrite
) -> Dict[str, Any]:
    return asyncio.run(
        get_deregister_response_status(metagraph, hotkey, regkey, dendrite)
    )
