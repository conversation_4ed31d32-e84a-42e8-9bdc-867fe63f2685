from typing import List, Dict, Any


def extract_miner_details(
    hotkey: str,
    details: Dict[str, Any],
    gpu_instances: Dict[str, Any],
    total_gpu_counts: Dict[str, Any],
    allocated_hotkeys: List[str],
):
    if details:  # Check if details are not empty
        try:
            # Extract GPU details
            gpu_miner = details["gpu"]
            gpu_capacity = "{:.2f}".format((gpu_miner["capacity"] / 1024))
            gpu_name = str(gpu_miner["details"][0]["name"]).lower()
            gpu_count = gpu_miner["count"]

            # Extract CPU details
            cpu_miner = details["cpu"]
            cpu_count = cpu_miner["count"]

            # Extract RAM details
            ram_miner = details["ram"]
            ram = "{:.2f}".format(ram_miner["available"] / 1024.0**3)

            # Extract Hard Disk details
            hard_disk_miner = details["hard_disk"]
            hard_disk = "{:.2f}".format(hard_disk_miner["free"] / 1024.0**3)

            # Update the GPU instances count
            gpu_key = (gpu_name, gpu_count)
            gpu_instances[gpu_key] = gpu_instances.get(gpu_key, 0) + 1
            total_gpu_counts[gpu_name] = total_gpu_counts.get(gpu_name, 0) + gpu_count

        except (KeyError, IndexError, TypeError):
            gpu_name = "Invalid details"
            gpu_capacity = "N/A"
            gpu_count = "N/A"
            cpu_count = "N/A"
            ram = "N/A"
            hard_disk = "N/A"
    else:
        return {
            "gpu_name": "No details available",
            "gpu_capacity": "N/A",
            "gpu_count": "N/A",
            "cpu_count": "N/A",
            "ram": "N/A",
            "hard_disk": "N/A",
            "status": "N/A",
        }

    # Allocation status
    # allocate_status = "N/A"

    if hotkey in allocated_hotkeys:
        allocate_status = "reserved"
    else:
        allocate_status = "available"

    return {
        "gpu_name": gpu_name,
        "gpu_capacity": gpu_capacity,
        "gpu_count": gpu_count,
        "cpu_count": cpu_count,
        "ram": ram,
        "hard_disk": hard_disk,
        "status": allocate_status,
    }
