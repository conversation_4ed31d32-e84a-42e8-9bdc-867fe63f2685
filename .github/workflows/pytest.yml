name: Pytest Tests

on:
  push:
    branches-ignore:
      - '!main'

jobs:
  pytest:
    name: Run Pytest Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e .[dev] -r requirements.txt -r requirements-dev.txt

      - name: Run Test
        env:
          CERT_LOCATION: ./cert/
        run: pytest
