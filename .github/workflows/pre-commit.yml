name: Pre-commit Checks

on:
  pull_request:
  push:
    branches: ["dev", "main"]

jobs:
  pre-commit:
    name: Run Pre-commit Hooks
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: |
          **/pyproject.toml
          **/requirements*.txt

    - name: Install dependencies
      run: pip install -e .[dev] -r requirements.txt -r requirements-dev.txt

    - name: Run pre-commit
      uses: pre-commit/action@v3.0.1
