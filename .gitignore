# Virtual Environment
.venv/
.venv-py310/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
dist/
sdist/

# Validators db.
/wandb/

# Cert files
cert/ca.cer
cert/ca.key
cert/client.cer
cert/client.crt
cert/client.key
cert/client.p12
cert/server.cer
cert/server.crt
cert/server.key

# Unit test / coverage reports
.coverage
.coverage.*
.pytest_cache/
.cache
htmlcov/
coverage.xml

.env
database.db
# Local template configuration overrides
config/templates.local.yaml
