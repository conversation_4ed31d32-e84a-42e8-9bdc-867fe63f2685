# register-api

- ### Preparation Steps

**1.** Run **`pip install -r requirements.txt`** in the same virtual environment as the validator, at the root of the repository

**2.** Run **`python -m pip install -e .`** at the root of the **`ni-compute`**(https://github.com/neuralinternet/ni-compute) to install the register-api dependency module.

**3.** Run **`sudo apt-get install npm`** to install npm, which is required for installing **`PM2 (Process Manager 2)`**

**4.** Run **`npm install -g pm2`** to install **`PM2`**, which helps keep applications running continuously and manage processes efficiently.

- ### Register API Execution Steps

**1.** Run Miner and Validator

**2.** Create a **`.env`** file by referring to the **`.env.example`** file, as shown below.
```
DEALLOCATION_NOTIFY_URL="https://dev.neuralinternet.ai/api/gpus/webhook/deallocation"
STATUS_NOTIFY_URL="https://dev.neuralinternet.ai/api/gpus/webhook/status-change-warning"
CERT_LOCATION="cert_file_path"
SQLITE_DB_PATH="sqlite_db_path"
```
- **`CERT_LOCATION`**: The cert files are originally created in ni-compute. If the files are missing, they need to be created using **`gen_ca.sh`**(https://github.com/neuralinternet/ni-compute/blob/main/cert/gen_ca.sh) in **`ni-compute`**.
- **`SQLITE_DB_PATH`**: The full path to the SQLite database on the ni-compute side, which is used by the validator.

**3.** Set **`PYTHONPATH`** before running the application, make sure to set the **`PYTHONPATH`** to include the necessary directories:

`export PYTHONPATH=$PYTHONPATH:$(pwd):$(pwd)/libs/register:$(pwd)/src`

**4.** Run the application by executing the **`main.py`** script with the required parameters, including the **`PYTHONPATH`** environment variable to ensure the necessary directories are included, and execute it through **`pm2`** for process management:

`pm2 start main.py --name register_api --interpreter python3 --env PYTHONPATH=$PYTHONPATH -- --netuid {netuid_number} --axon.port {your_desired_port} --axon.ip {your_private_ip} --subtensor.network {your_network_name} --wallet.name {your_wallet_name} --wallet.hotkey {your_wallet_hotkey_name} --logging.debug --logging.trace`

Replace the placeholders with actual values:

- **`your_desired_port`**: The port number you want to use.
- **`your_private_ip`**: Your private IP for Axon.
- **`your_network_name`**: The name of the network you're connecting to.
- **`your_wallet_name`**: The name of your wallet.
- **`your_wallet_hotkey_name`**: The hotkey name for your wallet.
