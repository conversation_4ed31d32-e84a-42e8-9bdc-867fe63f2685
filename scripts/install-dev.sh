#!/bin/bash

echo ">>> Installing Python development dependencies..."
pip install -e .[dev] -r requirements.txt -r requirements-dev.txt

echo ">>> Installing pre-commit git hooks..."
pre-commit install --hook-type commit-msg
pre-commit install --hook-type pre-commit
pre-commit install --hook-type pre-push
pre-commit install --hook-type post-checkout

echo ">>> Configuring git to ignore formatting revisions for blame..."
git config blame.ignoreRevsFile .git-blame-ignore-revs

echo ""
echo "✅ Development environment setup complete."
