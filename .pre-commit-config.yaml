repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.20.0
    hooks:
        - id: commitlint
          stages: [commit-msg]
          additional_dependencies: ['@commitlint/config-conventional']
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff-check

  # Local repository for a custom hook
  - repo: local
    hooks:
      - id: check-branch-name
        name: 'Check Branch Name'
        entry: ./scripts/check-current-branch.sh
        stages: [pre-commit, pre-push, manual]
        language: system
        files: /dev/null
        always_run: true
        verbose: true
  - repo: local
    hooks:
      - id: post-checkout-check
        name: 'Check Branch Name (Post-Checkout)'
        entry: ./scripts/check-current-branch.sh
        stages: [post-checkout]
        language: system
        always_run: true
        verbose: true
  - repo: local
    hooks:
      - id: pip-compile
        name: 'Resolve requirements'
        entry: python -m piptools compile --quiet -o requirements.txt pyproject.toml
        stages: [pre-commit, manual]
        language: system
        files: pyproject.toml
        verbose: true
        pass_filenames: false
  - repo: local
    hooks:
      - id: pip-compile-dev
        name: 'Resolve dev requirements'
        entry: python -m piptools compile --resolver=backtracking --extra dev -c requirements.txt -q -o requirements-dev.txt pyproject.toml
        stages: [pre-commit, manual]
        language: system
        files: pyproject.toml
        verbose: true
        pass_filenames: false
