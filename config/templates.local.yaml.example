# Local Template Configuration Override (Example)
#
# Copy this file to templates.local.yaml to override default template settings
# without committing changes to version control.
#
# The templates.local.yaml file (if it exists) will be loaded instead of templates.yaml
#
# Usage:
#   cp templates.local.yaml.example templates.local.yaml
#   # Edit templates.local.yaml to customize templates
#
# To disable a template temporarily, set enabled: false

templates:
  - template: default-ubuntu-pytorch
    description: Ubuntu-based pytorch 2.8.0+CUDA 12.8 preinstalled image
    image: nirepo/default-pytorch:2.8.0-cuda12.8-cudnn9-runtime
    digest: sha256:75c5f262ff46a0b4eb84d914c9f0740d5fc79478a366085fbfd6bce51f48cfe9
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  # Example: Temporarily disable a template
  - template: ollama-ssh
    description: Ollama LLM server for AI model deployment
    image: nirepo/ollama-ssh:latest
    digest: sha256:b689dee49d9b03753d6f76e5ab2f4d6b655d1080234bcc070b1f54e937f233c5
    internal_ports:
      ssh: 22
      external: 27015
    enabled: false  # Disabled for testing
