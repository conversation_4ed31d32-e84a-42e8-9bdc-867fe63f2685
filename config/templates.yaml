# Template Images Configuration
# This file defines the approved template images for the compute subnet.
# Expected digests are SHA256 manifest hashes from Docker Hub.
#
# To disable a template, set enabled: false
# Templates with enabled: false will still have their digests validated if deployed,
# but won't appear in the /list/templates endpoint.

templates:
  - template: default-ubuntu-pytorch
    description: Ubuntu-based pytorch 2.8.0+CUDA 12.8 preinstalled image
    image: nirepo/default-pytorch:2.8.0-cuda12.8-cudnn9-runtime
    digest: sha256:75c5f262ff46a0b4eb84d914c9f0740d5fc79478a366085fbfd6bce51f48cfe9
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: ollama-ssh
    description: Ollama LLM server for AI model deployment
    image: nirepo/ollama-ssh:latest
    digest: sha256:b689dee49d9b03753d6f76e5ab2f4d6b655d1080234bcc070b1f54e937f233c5
    internal_ports:
      ssh: 22
      external: 27015
    enabled: false  # Disabled due to known test failures (CSN-1080)

  - template: comfyui-ssh
    description: ComfyUI for AI image generation
    image: nirepo/comfyui-ssh:latest
    digest: sha256:312411098589ca6e8b557589f4dcf5be2a2367045f724ffe807befc4bedc14f1
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: automatic1111-ssh
    description: Automatic1111 Stable Diffusion WebUI
    image: nirepo/automatic1111-ssh:latest
    digest: sha256:5a6da72aabccd04a897706052191db61876dfdc5461b51feebfb1b61912b5e6a
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: scientific-ssh
    description: Scientific computing environment
    image: nirepo/scientific-ssh:latest
    digest: sha256:40c93cdcba24212a71f9348b65d79ed59f113996eed36711c5cb8d240fbebc7e
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: jupyter-scipy-ssh
    description: Jupyter notebook with SciPy stack
    image: nirepo/jupyter-scipy-ssh:latest
    digest: sha256:4831abb222b3ac6cdfa17b8d9a6efad10f5555cb5497548b201143e1218a923c
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: jupyter-spark-ssh
    description: Jupyter notebook with Apache Spark
    image: nirepo/jupyter-spark-ssh:latest
    digest: sha256:0ea465a32d093670374ffd883a39b45d2835043a0fc5507ad68e4c3838b4242f
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true

  - template: jupyter-tensorflow-ssh
    description: Jupyter notebook with TensorFlow
    image: nirepo/jupyter-tensorflow-ssh:latest
    digest: sha256:a11b46549c4dd26e06c8dd733d5ecfa1384211e6e0964c38420f5021fe5a9f6e
    internal_ports:
      ssh: 22
      external: 27015
    enabled: true
