# Standard Library Imports
import os
import bittensor as bt

# Compute Subnet Libraries
from compute.utils.parser import <PERSON>mpute<PERSON>rg<PERSON><PERSON><PERSON>
from typing import Dict, Any


def _init_config() -> Dict[str, Any]:
    """
    This function is responsible for setting up and parsing command-line arguments.
    :return: config
    """
    parser = ComputeArgPaser(
        description="This script aims to help allocation with the compute subnet."
    )
    parser.add_argument("--dev_tls_mode", action='store_true', help="Disables strict MTLS by making client certificates optional for dev deployment.")
    parser.config = bt.config(parser)
    config = parser.config
    # Step 3: Set up logging directory
    # Logging is crucial for monitoring and debugging purposes.
    config.full_path = os.path.expanduser(
        "{}/{}/{}/netuid{}/{}/{}/".format(
            config.logging.logging_dir,
            config.wallet.name,
            config.wallet.hotkey,
            config.netuid,
            "validator",
            "register",
        )
    )
    # Ensure the logging directory exists.
    if not os.path.exists(config.full_path):
        os.makedirs(config.full_path, exist_ok=True)
    # Return the parsed config.
    return config


# @staticmethod
# def _get_config(user_config: UserConfig, requirements: Union[DeviceRequirement, None] = None):
#     """
#     Get the config from user config and spec requirement for the API.
#     user_config: The user configuration which contain the validator's hotkey and wallet information.
#     requirements: The device requirements.
#     """
#     parser = argparse.ArgumentParser()
#     # Adds bittensor specific arguments
#     parser.add_argument(
#         "--netuid", type=int, default=27, help="The chain subnet uid."
#     )
#     # parser.add_argument("--gpu_type", type=str, help="The GPU type.")
#     # parser.add_argument("--gpu_size", type=int, help="The GPU memory in MB.")
#     # parser.add_argument("--timeline", type=int, help="The allocation timeline.")
#     bt.subtensor.add_args(parser)
#     bt.logging.add_args(parser)
#     bt.wallet.add_args(parser)
#     if not user_config.subtensor_chain_endpoint:
#         if user_config.subtensor_network == "finney":
#             user_config.subtensor_chain_endpoint = (
#                 "wss://entrypoint-finney.opentensor.ai:443"
#             )
#         elif user_config.subtensor_network == "test":
#             user_config.subtensor_chain_endpoint = (
#                 "wss://test.finney.opentensor.ai:443"
#             )
#     # Add user configuration and requirement to list for the bt config parser
#     args_list = []
#     for entry in [user_config, requirements]:
#         if entry:
#             for k, v in entry.__fields__.items():
#                 args_list.append(f"--{v.alias}")
#                 args_list.append(getattr(entry, k))
#     # Parse the initial config to check for provided arguments
#     config = bt.config(parser=parser, args=args_list)
#     # Set up logging directory
#     config.full_path = os.path.expanduser(
#         "{}/{}/{}/netuid{}/{}".format(
#             config.logging.logging_dir,
#             config.wallet.name,
#             config.wallet.hotkey,
#             config.netuid,
#             "validator",
#         )
#     )
#     if not os.path.exists(config.full_path):
#         os.makedirs(config.full_path, exist_ok=True)
#     return config
