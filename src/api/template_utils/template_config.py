"""
Template Configuration Loader

Loads template configuration from YAML file with fallback to hardcoded defaults.
This allows easy updating of template list without code changes.
"""

import os
import yaml
import <PERSON><PERSON> as bt
from pathlib import Path


def get_templates_config_path():
    """
    Get the path to templates config file.

    Priority order:
    1. Environment variable TEMPLATES_CONFIG_PATH
    2. Local override: config/templates.local.yaml (gitignored)
    3. Default: config/templates.yaml (committed to repo)
    """
    # 1. Try environment variable first (highest priority)
    env_path = os.getenv("TEMPLATES_CONFIG_PATH")
    if env_path and Path(env_path).exists():
        return Path(env_path)

    # 2. Try local override (not in git)
    local_config_path = Path(__file__).parent.parent.parent.parent / "config" / "templates.local.yaml"
    if local_config_path.exists():
        return local_config_path

    # 3. Default config (in git)
    config_path = Path(__file__).parent.parent.parent.parent / "config" / "templates.yaml"
    if config_path.exists():
        return config_path

    return None


def load_templates_config():
    """
    Load templates configuration from YAML file with fallback to defaults.

    Returns:
        dict: {
            "templates": list[dict],  # For list_templates endpoint (enabled only)
            "digests": dict[str, str]  # For validation (all templates)
        }
    """
    config_path = get_templates_config_path()

    if config_path:
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            templates_list = []
            digests_dict = {}

            for template in config.get('templates', []):
                image = template.get('image')
                digest = template.get('digest')

                # Add to digests dict for validation (all templates, even disabled)
                if image and digest:
                    digests_dict[image] = digest

                # Add to templates list only if enabled
                if template.get('enabled', True):
                    templates_list.append({
                        "template": template.get('template'),
                        "description": template.get('description'),
                        "image": image,
                        "internal_ports": template.get('internal_ports', {"ssh": 22, "external": 27015})
                    })

            bt.logging.info(f"Loaded {len(templates_list)} enabled templates from {config_path}")
            bt.logging.debug(f"Total templates with digests: {len(digests_dict)}")

            return {
                "templates": templates_list,
                "digests": digests_dict
            }

        except Exception as e:
            bt.logging.error(f"Error loading templates config from {config_path}: {e}")
            bt.logging.warning("Falling back to hardcoded template configuration")

    # Fallback to hardcoded configuration
    return get_default_templates_config()


def get_default_templates_config():
    """
    Hardcoded fallback configuration.
    Used when YAML config file is not available.
    """
    templates = [
        {
            "template": "default-ubuntu-pytorch",
            "description": "Ubuntu-based pytorch 2.8.0+CUDA 12.8 preinstalled image",
            "image": "nirepo/default-pytorch:2.8.0-cuda12.8-cudnn9-runtime",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "comfyui-ssh",
            "description": "ComfyUI for AI image generation",
            "image": "nirepo/comfyui-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "automatic1111-ssh",
            "description": "Automatic1111 Stable Diffusion WebUI",
            "image": "nirepo/automatic1111-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "scientific-ssh",
            "description": "Scientific computing environment",
            "image": "nirepo/scientific-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "jupyter-scipy-ssh",
            "description": "Jupyter notebook with SciPy stack",
            "image": "nirepo/jupyter-scipy-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "jupyter-spark-ssh",
            "description": "Jupyter notebook with Apache Spark",
            "image": "nirepo/jupyter-spark-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
        {
            "template": "jupyter-tensorflow-ssh",
            "description": "Jupyter notebook with TensorFlow",
            "image": "nirepo/jupyter-tensorflow-ssh:latest",
            "internal_ports": {"ssh": 22, "external": 27015},
        },
    ]

    digests = {
        'nirepo/default-pytorch:2.8.0-cuda12.8-cudnn9-runtime': 'sha256:75c5f262ff46a0b4eb84d914c9f0740d5fc79478a366085fbfd6bce51f48cfe9',
        'nirepo/ollama-ssh:latest': 'sha256:b689dee49d9b03753d6f76e5ab2f4d6b655d1080234bcc070b1f54e937f233c5',
        'nirepo/comfyui-ssh:latest': 'sha256:312411098589ca6e8b557589f4dcf5be2a2367045f724ffe807befc4bedc14f1',
        'nirepo/automatic1111-ssh:latest': 'sha256:5a6da72aabccd04a897706052191db61876dfdc5461b51feebfb1b61912b5e6a',
        'nirepo/scientific-ssh:latest': 'sha256:40c93cdcba24212a71f9348b65d79ed59f113996eed36711c5cb8d240fbebc7e',
        'nirepo/jupyter-scipy-ssh:latest': 'sha256:4831abb222b3ac6cdfa17b8d9a6efad10f5555cb5497548b201143e1218a923c',
        'nirepo/jupyter-spark-ssh:latest': 'sha256:0ea465a32d093670374ffd883a39b45d2835043a0fc5507ad68e4c3838b4242f',
        'nirepo/jupyter-tensorflow-ssh:latest': 'sha256:a11b46549c4dd26e06c8dd733d5ecfa1384211e6e0964c38420f5021fe5a9f6e',
    }

    return {
        "templates": templates,
        "digests": digests
    }


# Load configuration on module import
_templates_config = load_templates_config()
TEMPLATE_EXPECTED_DIGESTS = _templates_config["digests"]
ENABLED_TEMPLATES = _templates_config["templates"]
