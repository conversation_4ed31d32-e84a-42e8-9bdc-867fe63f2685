# from compute.utils.socket import check_port
# import bittensor as bt
from typing import Dict, Any, List


def _paginate_list(
    items: List[Any], page_number: int, page_size: int
) -> Dict[str, Any]:
    # Calculate the start and end indices of the items on the current page
    start_index = (page_number - 1) * page_size
    end_index = start_index + page_size
    # Get the items on the current page
    page_items = items[start_index:end_index]
    # Determine if there are more pages
    has_next_page = end_index < len(items)
    next_page_number = page_number + 1 if has_next_page else None
    return {
        "page_items": page_items,
        "page_number": page_number,
        "page_size": page_size,
        "next_page_number": next_page_number,
    }


# def check_port_open(host, port, hotkey):
#     result = check_port(host, port)
#     if result is True:
#         bt.logging.info(f"API: Port {port} on {host} is open for {hotkey}")
#         return True
#     elif result is False:
#         bt.logging.info(f"API: Port {port} on {host} is closed for {hotkey}")
#         return False
#     else:
#         bt.logging.warning(f"API: Could not determine status of port {port} on {host} for {hotkey}")
#         return False
