import bittensor as bt
from geoip2fast import GeoIP2Fast


def geoip_update():
    geoip = GeoIP2Fast()
    geoip.update_all(verbose=True)
    bt.logging.info("GeoIP DB updated.")
    geoip.reload_data(verbose=True)
    bt.logging.info(f"{geoip.startup_line_text}")


def geoip_lookup(ip: str | None) -> dict:
    geoip = GeoIP2Fast()
    if not ip:
        return None
    if not (match := geoip.lookup(ip)):
        return None
    return match.pp_json()
