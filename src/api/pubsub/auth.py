"""
Authentication provider for direct service account based Pub/Sub communication.

This module provides authentication using Google Cloud service account credentials
instead of the SN27 token gateway flow.
"""

import json
import logging
import os
from google.oauth2 import service_account
from google.auth import default

from compute.pubsub.exceptions import AuthenticationError, ConfigurationError


class ServiceAccountAuth:
    """
    Authentication provider for direct service account credentials.
    
    This class handles authentication using:
    1. Service account key file (if provided)
    2. Default application credentials (if available)
    3. Environment variables (GOOGLE_APPLICATION_CREDENTIALS)
    """
    
    def __init__(self, credentials_path: str | None = None, project_id: str | None = None):
        """
        Initialize the service account auth provider.
        
        Args:
            credentials_path: Path to service account key file (optional)
            project_id: GCP project ID (optional, will be extracted from credentials)
        """
        self.credentials_path = credentials_path
        self.project_id = project_id
        self.logger = logging.getLogger(__name__)
        
        self._credentials = None
        self._extracted_project_id = None
        
        # Initialize credentials
        self._initialize_credentials()
    
    def _initialize_credentials(self):
        """Initialize credentials from various sources."""
        try:
            # Try explicit credentials path first
            if self.credentials_path and os.path.exists(self.credentials_path):
                self.logger.info(f"Using service account credentials from: {self.credentials_path}")
                self._credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_path,
                    scopes=["https://www.googleapis.com/auth/pubsub"]
                )
                # Extract project ID from service account file
                with open(self.credentials_path, 'r') as f:
                    service_account_info = json.load(f)
                    self._extracted_project_id = service_account_info.get('project_id')
                    
            # Try environment variable
            elif os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
                credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
                self.logger.info(f"Using service account credentials from GOOGLE_APPLICATION_CREDENTIALS: {credentials_path}")
                self._credentials = service_account.Credentials.from_service_account_file(
                    credentials_path,
                    scopes=["https://www.googleapis.com/auth/pubsub"]
                )
                # Extract project ID from service account file
                with open(credentials_path, 'r') as f:
                    service_account_info = json.load(f)
                    self._extracted_project_id = service_account_info.get('project_id')
                    
            # Try default application credentials
            else:
                self.logger.info("Using default application credentials")
                self._credentials, default_project = default(
                    scopes=["https://www.googleapis.com/auth/pubsub"]
                )
                self._extracted_project_id = default_project
                
        except Exception as e:
            raise AuthenticationError(f"Failed to initialize service account credentials: {e}") from e
    
    def get_credentials(self):
        """
        Get service account credentials for pubsub clients.
        
        Returns:
            Google Cloud credentials for pubsub clients
        """
        if not self._credentials:
            raise AuthenticationError("No credentials available")
        return self._credentials
    
    def get_project_id(self) -> str:
        """Get the project ID."""
        # Use explicit project_id if provided
        if self.project_id:
            return self.project_id
            
        # Use extracted project_id from credentials
        if self._extracted_project_id:
            return self._extracted_project_id
            
        # Try environment variable
        if os.getenv('PUBSUB_PROJECT_ID'):
            return os.getenv('PUBSUB_PROJECT_ID')
            
        raise ConfigurationError(
            "No project ID available. Please provide project_id parameter, "
            "set PUBSUB_PROJECT_ID environment variable, or use service account "
            "credentials that include project_id."
        )
    
    def refresh_credentials(self):
        """Refresh credentials if needed."""
        # Service account credentials don't need manual refresh
        # They are automatically refreshed by the Google client libraries
        self.logger.debug("Service account credentials refresh requested (no action needed)")
