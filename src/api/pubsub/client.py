"""
Register API Pub/Sub client using service account authentication.

This module provides a PubSubClient that inherits from SN27's implementation
but uses direct service account credentials instead of the SN27 token gateway.
"""

import os
import logging

# Import the SN27 PubSubClient to inherit from
from compute.pubsub.client import PubS<PERSON>Client as SN27PubSubClient
from compute.pubsub.exceptions import ConfigurationError

from .auth import ServiceAccountAuth


class PubSubClient(SN27PubSubClient):
    """
    Pub/Sub client that inherits from SN27 but uses service account authentication.

    This client provides the exact same interface as SN27's PubSubClient but
    uses direct Google Cloud service account credentials instead of the
    SN27 token gateway authentication flow.
    """

    def __init__(
        self,
        credentials_path: str | None = None,
        project_id: str | None = None,
        timeout: float = 30.0,
        auto_refresh_interval: int = 600,
        validator_hotkey: str | None = None,
    ):
        """
        Initialize the service account pub/sub client.

        Args:
            credentials_path: Path to service account key file (optional)
            project_id: GCP project ID (optional)
            timeout: Timeout for publish operations
            auto_refresh_interval: Interval for credential refresh (not used for service accounts)
            validator_hotkey: Validator hotkey for message factory
        """
        self.logger = logging.getLogger(__name__)

        # Check if pubsub is disabled
        self.disabled = (
            os.getenv('PUBSUB_DISABLE', '').lower() in ('1', 'true', 'yes', 'y', 'on')
        )
        if self.disabled:
            self.logger.info("PubSub functionality disabled via PUBSUB_DISABLE environment variable")
            # Still call parent init to set up the structure
            super().__init__(
                wallet=None,
                config=None,
                timeout=timeout,
                auto_refresh_interval=auto_refresh_interval
            )
            return

        # Initialize service account authentication
        try:
            self.auth = ServiceAccountAuth(credentials_path, project_id)
            self.project_id = self.auth.get_project_id()
            self.validator_hotkey = validator_hotkey or "register-api"

            self.logger.info(f"Initialized service account auth for project: {self.project_id}")

        except Exception as e:
            raise ConfigurationError(f"Failed to initialize service account authentication: {e}") from e

        # Create a minimal config-like object for parent class
        class MockConfig:
            def __init__(self):
                self.pubsub_disabled = False

        class MockWallet:
            def __init__(self, hotkey_address):
                self.hotkey = MockHotkey(hotkey_address)

        class MockHotkey:
            def __init__(self, address):
                self.ss58_address = address

        # Initialize parent with mock objects
        mock_wallet = MockWallet(self.validator_hotkey)
        mock_config = MockConfig()

        # Call parent constructor
        super().__init__(
            wallet=mock_wallet,
            config=mock_config,
            timeout=timeout,
            auto_refresh_interval=auto_refresh_interval
        )

    def _initialize_clients(self, max_retries: int = 3):
        """Override parent method to use service account credentials."""
        if self.disabled:
            return

        try:
            credentials = self.auth.get_credentials()

            # Import here to avoid circular imports
            from google.cloud import pubsub_v1

            # Initialize clients with service account credentials
            self.publisher = pubsub_v1.PublisherClient(credentials=credentials)
            self.subscriber = pubsub_v1.SubscriberClient(credentials=credentials)

            self.logger.info("Successfully initialized Pub/Sub client with service account credentials")

        except Exception as e:
            self.logger.error(f"Failed to initialize Pub/Sub clients: {e}")
            raise

    def refresh_credentials(self, max_retries: int = 5) -> bool:
        """
        Override parent method for service account credential refresh.

        Service account credentials are automatically refreshed by the Google
        client libraries, so this is mostly a no-op.

        Returns:
            True (service accounts don't need manual refresh)
        """
        if self.disabled:
            return True

        try:
            self.auth.refresh_credentials()
            self.logger.info("Service account credentials refreshed (automatic)")
            return True
        except Exception as e:
            self.logger.error(f"Error during credential refresh: {e}")
            return False


