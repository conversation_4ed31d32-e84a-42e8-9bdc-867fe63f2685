"""
Register API Pub/Sub Library

A service account-based wrapper around SN27's PubSub client. This library
inherits from the SN27 PubSubClient but uses direct service account authentication
instead of the SN27 token gateway.

Usage:
    from api.pubsub import PubSubClient

    # Initialize with service account credentials (same interface as SN27)
    client = PubSubClient(
        credentials_path="/path/to/service-account.json",  # Optional
        project_id="your-project-id",  # Optional
        timeout=30.0
    )

    # All SN27 methods work exactly the same
    await client.publish_to_miner_events(message)
"""

from .client import PubSubClient
from .auth import ServiceAccountAuth

# Re-export everything from SN27 for compatibility
from compute.pubsub import (
    MessageFactory,
    create_allocation_ended_message,
    create_miner_offline_message,
    create_miner_online_message,
    BasePubSubMessage,
    MinerAllocationMessage,
    MinerDeallocationMessage,
    PogResultMessage,
    TOPICS,
    MESSAGE_TYPES,
    PubSubError,
    MessageValidationError,
    PublishError,
)

__version__ = "1.0.0"
__author__ = "Register API Team"

__all__ = [
    "PubSubClient",
    "ServiceAccountAuth",
    # Re-exported from SN27
    "create_allocation_ended_message",
    "create_miner_offline_message",
    "create_miner_online_message",
    "MessageFactory",
    "PubSubError",
    "MessageValidationError",
    "PublishError",
    "BasePubSubMessage",
    "MinerAllocationMessage",
    "MinerDeallocationMessage",
    "PogResultMessage",
    "TOPICS",
    "MESSAGE_TYPES",
]
