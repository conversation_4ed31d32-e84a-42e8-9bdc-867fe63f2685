#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --constraint=requirements.txt --extra=dev --output-file=requirements-dev.txt pyproject.toml
#
aiohappyeyeballs==2.4.8
    # via
    #   -c requirements.txt
    #   aiohttp
aiohttp==3.10.11
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
aiosignal==1.3.2
    # via
    #   -c requirements.txt
    #   aiohttp
allure-pytest==2.14.2
    # via register-api (pyproject.toml)
allure-python-commons==2.14.2
    # via allure-pytest
altgraph==0.17.4
    # via
    #   -c requirements.txt
    #   pyinstaller
annotated-types==0.7.0
    # via
    #   -c requirements.txt
    #   pydantic
anyio==4.8.0
    # via
    #   -c requirements.txt
    #   httpx
    #   starlette
async-property==0.2.2
    # via
    #   -c requirements.txt
    #   bittensor-cli
async-substrate-interface==1.0.3
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
async-timeout==5.0.1
    # via
    #   -c requirements.txt
    #   aiohttp
asyncstdlib==3.13.0
    # via
    #   -c requirements.txt
    #   async-substrate-interface
    #   bittensor
at==0.0.3
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
attrs==25.1.0
    # via
    #   -c requirements.txt
    #   aiohttp
    #   allure-python-commons
backoff==2.2.1
    # via
    #   -c requirements.txt
    #   bittensor-cli
base58==2.1.1
    # via
    #   -c requirements.txt
    #   scalecodec
bcrypt==4.3.0
    # via
    #   -c requirements.txt
    #   paramiko
bittensor==9.0.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
bittensor-cli==9.1.0
    # via
    #   -c requirements.txt
    #   bittensor
bittensor-commit-reveal==0.2.0
    # via
    #   -c requirements.txt
    #   bittensor
bittensor-wallet==3.0.4
    # via
    #   -c requirements.txt
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
black==23.7.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
blake3==1.0.4
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
bt-decode==0.5.0a2
    # via
    #   -c requirements.txt
    #   async-substrate-interface
build==1.2.2.post1
    # via pip-tools
cachetools==5.5.2
    # via
    #   -c requirements.txt
    #   google-auth
certifi==2025.1.31
    # via
    #   -c requirements.txt
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via
    #   -c requirements.txt
    #   cryptography
    #   pynacl
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.1
    # via
    #   -c requirements.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements.txt
    #   black
    #   pip-tools
    #   typer
    #   uvicorn
    #   wandb
colorama==0.4.6
    # via
    #   -c requirements.txt
    #   bittensor
    #   pytest-depends
coverage[toml]==7.9.2
    # via pytest-cov
cryptography==43.0.1
    # via
    #   -c requirements.txt
    #   bittensor-wallet
    #   paramiko
    #   register-api (pyproject.toml)
cytoolz==1.0.1
    # via
    #   -c requirements.txt
    #   eth-utils
decorator==5.2.1
    # via
    #   -c requirements.txt
    #   retry
defusedxml==0.7.1
    # via
    #   -c requirements.txt
    #   ipwhois
distlib==0.3.9
    # via virtualenv
dnspython==2.7.0
    # via
    #   -c requirements.txt
    #   ipwhois
docker==7.0.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
docker-pycreds==0.4.0
    # via
    #   -c requirements.txt
    #   wandb
eth-hash==0.7.1
    # via
    #   -c requirements.txt
    #   eth-utils
eth-typing==5.2.0
    # via
    #   -c requirements.txt
    #   eth-utils
eth-utils==2.2.2
    # via
    #   -c requirements.txt
    #   bittensor-wallet
exceptiongroup==1.3.0
    # via
    #   -c requirements.txt
    #   anyio
    #   pytest
fastapi==0.110.3
    # via
    #   -c requirements.txt
    #   bittensor
filelock==3.17.0
    # via
    #   -c requirements.txt
    #   torch
    #   triton
    #   virtualenv
frozenlist==1.5.0
    # via
    #   -c requirements.txt
    #   aiohttp
    #   aiosignal
fsspec==2025.2.0
    # via
    #   -c requirements.txt
    #   torch
future-fstrings==1.2.0
    # via pytest-depends
fuzzywuzzy==0.18.0
    # via
    #   -c requirements.txt
    #   bittensor-cli
geoip2fast==1.2.2
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
gitdb==4.0.12
    # via
    #   -c requirements.txt
    #   gitpython
gitpython==3.1.44
    # via
    #   -c requirements.txt
    #   bittensor-cli
    #   wandb
google-api-core[grpc]==2.25.1
    # via
    #   -c requirements.txt
    #   google-cloud-core
    #   google-cloud-pubsub
google-auth==2.40.3
    # via
    #   -c requirements.txt
    #   google-api-core
    #   google-cloud-core
    #   google-cloud-pubsub
    #   register-api (pyproject.toml)
google-cloud-core==2.4.3
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
google-cloud-pubsub==2.31.1
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
googleapis-common-protos[grpc]==1.70.0
    # via
    #   -c requirements.txt
    #   google-api-core
    #   grpc-google-iam-v1
    #   grpcio-status
gputil==1.4.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
grpc-google-iam-v1==0.14.2
    # via
    #   -c requirements.txt
    #   google-cloud-pubsub
grpcio==1.74.0
    # via
    #   -c requirements.txt
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.71.2
    # via
    #   -c requirements.txt
    #   google-api-core
    #   google-cloud-pubsub
h11==0.14.0
    # via
    #   -c requirements.txt
    #   httpcore
    #   register-api (pyproject.toml)
    #   uvicorn
httpcore==1.0.8
    # via
    #   httpx
    #   register-api (pyproject.toml)
httpx==0.28.1
    # via register-api (pyproject.toml)
identify==2.6.12
    # via pre-commit
idna==3.10
    # via
    #   -c requirements.txt
    #   anyio
    #   httpx
    #   requests
    #   yarl
igpu==0.1.2
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
importlib-metadata==8.7.0
    # via
    #   -c requirements.txt
    #   opentelemetry-api
iniconfig==2.0.0
    # via
    #   -c requirements.txt
    #   pytest
ipwhois==1.3.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
jinja2==3.1.5
    # via
    #   -c requirements.txt
    #   bittensor-cli
    #   torch
levenshtein==0.27.1
    # via
    #   -c requirements.txt
    #   python-levenshtein
markdown-it-py==3.0.0
    # via
    #   -c requirements.txt
    #   rich
markupsafe==3.0.2
    # via
    #   -c requirements.txt
    #   jinja2
mdurl==0.1.2
    # via
    #   -c requirements.txt
    #   markdown-it-py
more-itertools==10.6.0
    # via
    #   -c requirements.txt
    #   scalecodec
mpmath==1.3.0
    # via
    #   -c requirements.txt
    #   sympy
msgpack==1.1.0
    # via
    #   -c requirements.txt
    #   msgpack-numpy-opentensor
msgpack-numpy-opentensor==0.5.0
    # via
    #   -c requirements.txt
    #   bittensor
multidict==6.1.0
    # via
    #   -c requirements.txt
    #   aiohttp
    #   yarl
munch==2.5.0
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-wallet
mypy-extensions==1.0.0
    # via
    #   -c requirements.txt
    #   black
narwhals==1.29.0
    # via
    #   -c requirements.txt
    #   plotly
nest-asyncio==1.6.0
    # via
    #   -c requirements.txt
    #   bittensor
netaddr==1.3.0
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
networkx==3.4.2
    # via
    #   -c requirements.txt
    #   pytest-depends
    #   torch
nodeenv==1.9.1
    # via pre-commit
numpy==2.0.2
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
    #   msgpack-numpy-opentensor
    #   register-api (pyproject.toml)
nvidia-cublas-cu12==12.4.5.8
    # via
    #   -c requirements.txt
    #   nvidia-cudnn-cu12
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cuda-cupti-cu12==12.4.127
    # via
    #   -c requirements.txt
    #   torch
nvidia-cuda-nvrtc-cu12==12.4.127
    # via
    #   -c requirements.txt
    #   torch
nvidia-cuda-runtime-cu12==12.4.127
    # via
    #   -c requirements.txt
    #   torch
nvidia-cudnn-cu12==9.1.0.70
    # via
    #   -c requirements.txt
    #   torch
nvidia-cufft-cu12==11.2.1.3
    # via
    #   -c requirements.txt
    #   torch
nvidia-curand-cu12==10.3.5.147
    # via
    #   -c requirements.txt
    #   torch
nvidia-cusolver-cu12==11.6.1.9
    # via
    #   -c requirements.txt
    #   torch
nvidia-cusparse-cu12==12.3.1.170
    # via
    #   -c requirements.txt
    #   nvidia-cusolver-cu12
    #   torch
nvidia-ml-py==12.570.86
    # via
    #   -c requirements.txt
    #   pynvml
nvidia-nccl-cu12==2.21.5
    # via
    #   -c requirements.txt
    #   torch
nvidia-nvjitlink-cu12==12.4.127
    # via
    #   -c requirements.txt
    #   nvidia-cusolver-cu12
    #   nvidia-cusparse-cu12
    #   torch
nvidia-nvtx-cu12==12.4.127
    # via
    #   -c requirements.txt
    #   torch
opentelemetry-api==1.36.0
    # via
    #   -c requirements.txt
    #   google-cloud-pubsub
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-sdk==1.36.0
    # via
    #   -c requirements.txt
    #   google-cloud-pubsub
opentelemetry-semantic-conventions==0.57b0
    # via
    #   -c requirements.txt
    #   opentelemetry-sdk
packaging==24.2
    # via
    #   -c requirements.txt
    #   bittensor
    #   black
    #   build
    #   docker
    #   plotly
    #   pyinstaller
    #   pyinstaller-hooks-contrib
    #   pytest
paramiko==3.4.1
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
password-strength==0.0.3.post2
    # via
    #   -c requirements.txt
    #   bittensor-wallet
pathspec==0.12.1
    # via
    #   -c requirements.txt
    #   black
pip-tools==7.4.1
    # via register-api (pyproject.toml)
platformdirs==4.3.6
    # via
    #   -c requirements.txt
    #   black
    #   virtualenv
    #   wandb
plotille==5.0.0
    # via
    #   -c requirements.txt
    #   bittensor-cli
plotly==6.0.0
    # via
    #   -c requirements.txt
    #   bittensor-cli
pluggy==1.5.0
    # via
    #   -c requirements.txt
    #   allure-python-commons
    #   pytest
    #   pytest-cov
pre-commit==4.2.0
    # via register-api (pyproject.toml)
propcache==0.3.0
    # via
    #   -c requirements.txt
    #   yarl
proto-plus==1.26.1
    # via
    #   -c requirements.txt
    #   google-api-core
    #   google-cloud-pubsub
protobuf==5.29.3
    # via
    #   -c requirements.txt
    #   google-api-core
    #   google-cloud-pubsub
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
    #   wandb
psutil==5.9.8
    # via
    #   -c requirements.txt
    #   igpu
    #   register-api (pyproject.toml)
    #   wandb
py==1.11.0
    # via
    #   -c requirements.txt
    #   retry
py-bip39-bindings==0.1.11
    # via
    #   -c requirements.txt
    #   bittensor-wallet
pyasn1==0.6.1
    # via
    #   -c requirements.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   -c requirements.txt
    #   google-auth
pycparser==2.22
    # via
    #   -c requirements.txt
    #   cffi
pycryptodome==3.21.0
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
pydantic==2.10.6
    # via
    #   -c requirements.txt
    #   bittensor
    #   fastapi
    #   wandb
pydantic-core==2.27.2
    # via
    #   -c requirements.txt
    #   pydantic
pyfiglet==1.0.2
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
pygments==2.19.1
    # via
    #   -c requirements.txt
    #   rich
pyinstaller==6.4.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
pyinstaller-hooks-contrib==2025.1
    # via
    #   -c requirements.txt
    #   pyinstaller
pynacl==1.5.0
    # via
    #   -c requirements.txt
    #   paramiko
pynvml==12.0.0
    # via
    #   -c requirements.txt
    #   igpu
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   -c requirements.txt
    #   allure-pytest
    #   bittensor-cli
    #   pytest-cov
    #   pytest-depends
    #   pytest-dotenv
    #   pytest-mock
    #   register-api (pyproject.toml)
pytest-cov==6.2.1
    # via register-api (pyproject.toml)
pytest-depends==1.0.1
    # via register-api (pyproject.toml)
pytest-dotenv==0.5.2
    # via register-api (pyproject.toml)
pytest-mock==3.14.1
    # via register-api (pyproject.toml)
python-dotenv==1.0.1
    # via
    #   -c requirements.txt
    #   pytest-dotenv
    #   register-api (pyproject.toml)
python-levenshtein==0.27.1
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
python-statemachine==2.5.0
    # via
    #   -c requirements.txt
    #   bittensor
pywry==0.6.2
    # via
    #   -c requirements.txt
    #   bittensor-cli
pyyaml==6.0.2
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
    #   pre-commit
    #   wandb
rapidfuzz==3.12.2
    # via
    #   -c requirements.txt
    #   levenshtein
requests==2.31.0
    # via
    #   -c requirements.txt
    #   bittensor
    #   docker
    #   google-api-core
    #   register-api (pyproject.toml)
    #   scalecodec
    #   wandb
retry==0.9.2
    # via
    #   -c requirements.txt
    #   bittensor
rich==13.9.4
    # via
    #   -c requirements.txt
    #   bittensor
    #   bittensor-cli
    #   bittensor-wallet
    #   typer
rsa==4.9.1
    # via
    #   -c requirements.txt
    #   google-auth
ruff==0.12.4
    # via register-api (pyproject.toml)
scalecodec==1.2.11
    # via
    #   -c requirements.txt
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
sentry-sdk==2.22.0
    # via
    #   -c requirements.txt
    #   wandb
setproctitle==1.3.5
    # via
    #   -c requirements.txt
    #   pywry
    #   wandb
shellingham==1.5.4
    # via
    #   -c requirements.txt
    #   typer
six==1.17.0
    # via
    #   -c requirements.txt
    #   docker-pycreds
    #   munch
    #   password-strength
smmap==5.0.2
    # via
    #   -c requirements.txt
    #   gitdb
sniffio==1.3.1
    # via
    #   -c requirements.txt
    #   anyio
starlette==0.37.2
    # via
    #   -c requirements.txt
    #   fastapi
sympy==1.13.1
    # via
    #   -c requirements.txt
    #   torch
termcolor==2.5.0
    # via
    #   -c requirements.txt
    #   bittensor-wallet
toml==0.10.0
    # via
    #   -c requirements.txt
    #   bt-decode
tomli==2.2.1
    # via
    #   -c requirements.txt
    #   black
    #   build
    #   coverage
    #   pip-tools
    #   pytest
toolz==1.0.0
    # via
    #   -c requirements.txt
    #   cytoolz
torch==2.5.1
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
triton==3.1.0
    # via
    #   -c requirements.txt
    #   torch
typer==0.15.2
    # via
    #   -c requirements.txt
    #   bittensor-cli
typing-extensions==4.12.2
    # via
    #   -c requirements.txt
    #   anyio
    #   eth-typing
    #   exceptiongroup
    #   fastapi
    #   multidict
    #   opentelemetry-api
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   rich
    #   torch
    #   typer
    #   uvicorn
    #   wandb
urllib3==2.3.0
    # via
    #   -c requirements.txt
    #   docker
    #   requests
    #   sentry-sdk
uvicorn==0.34.0
    # via
    #   -c requirements.txt
    #   bittensor
virtualenv==20.31.2
    # via pre-commit
wandb==0.19.0
    # via
    #   -c requirements.txt
    #   register-api (pyproject.toml)
websockets==15.0
    # via
    #   -c requirements.txt
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
wheel==0.45.1
    # via
    #   -c requirements.txt
    #   async-substrate-interface
    #   bittensor
    #   bittensor-cli
    #   pip-tools
xxhash==3.5.0
    # via
    #   -c requirements.txt
    #   async-substrate-interface
yarl==1.18.3
    # via
    #   -c requirements.txt
    #   aiohttp
zipp==3.23.0
    # via
    #   -c requirements.txt
    #   importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# pip
# setuptools
